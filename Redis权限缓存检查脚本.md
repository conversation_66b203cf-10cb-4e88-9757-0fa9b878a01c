# Redis权限缓存检查脚本

## 连接Redis
```bash
# 根据配置文件，Redis运行在本地6379端口，密码为password
redis-cli -h 127.0.0.1 -p 6379 -a password
```

## 检查权限缓存

### 1. 查看所有角色权限缓存
```redis
# 查看角色权限缓存的所有键值对
HGETALL "system:role_perms"
```

### 2. 查看特定角色的权限
```redis
# 查看特定角色的权限（请替换为实际角色编码）
HGET "system:role_perms" "EMPLOYEE"
HGET "system:role_perms" "MANAGER" 
HGET "system:role_perms" "ADMIN"
HGET "system:role_perms" "ROOT"
```

### 3. 检查权限缓存是否存在
```redis
# 检查权限缓存键是否存在
EXISTS "system:role_perms"

# 查看缓存过期时间
TTL "system:role_perms"
```

### 4. 查看用户会话信息
```redis
# 查看所有用户会话相关的键
KEYS "*user*"
KEYS "*token*"
KEYS "*session*"
```

## 缓存问题排查

### 问题1: 权限缓存为空
```redis
# 检查缓存是否为空
HLEN "system:role_perms"

# 如果为空，可能需要重启应用或手动刷新缓存
```

### 问题2: 特定角色权限缺失
```redis
# 检查所有角色
HKEYS "system:role_perms"

# 如果某个角色不存在，说明该角色的权限未被缓存
```

### 问题3: 权限数据格式错误
```redis
# 查看权限数据的具体格式
HGET "system:role_perms" "角色编码"

# 正常情况下应该返回权限字符串的集合，如：
# ["leave:application:query","leave:application:add","leave:type:query"]
```

## 缓存清理和刷新

### 清除权限缓存
```redis
# 删除角色权限缓存
DEL "system:role_perms"

# 删除所有用户会话（强制重新登录）
KEYS "*user*" | xargs redis-cli DEL
KEYS "*token*" | xargs redis-cli DEL
```

### 清除特定用户缓存
```redis
# 查找特定用户的缓存键
KEYS "*用户名*"

# 删除特定用户的缓存
DEL "具体的缓存键"
```

## PowerShell脚本（Windows环境）

### 检查Redis连接
```powershell
# 测试Redis连接
$redisHost = "127.0.0.1"
$redisPort = 6379
$redisPassword = "password"

# 使用redis-cli检查连接
redis-cli -h $redisHost -p $redisPort -a $redisPassword ping
```

### 批量检查权限缓存
```powershell
# 创建检查脚本
$checkScript = @"
HGETALL system:role_perms
HKEYS system:role_perms
EXISTS system:role_perms
TTL system:role_perms
"@

# 执行检查
$checkScript | redis-cli -h 127.0.0.1 -p 6379 -a password
```

## 常见Redis命令

### 基础操作
```redis
# 查看Redis信息
INFO

# 查看所有键
KEYS *

# 查看特定模式的键
KEYS "system:*"
KEYS "*role*"
KEYS "*perm*"

# 查看键的类型
TYPE "system:role_perms"

# 查看哈希表的所有字段
HKEYS "system:role_perms"

# 查看哈希表的所有值
HVALS "system:role_perms"
```

### 调试操作
```redis
# 监控Redis命令执行
MONITOR

# 查看慢查询日志
SLOWLOG GET 10

# 查看客户端连接
CLIENT LIST
```

## 权限缓存数据结构

### 正常的权限缓存结构
```json
{
  "system:role_perms": {
    "EMPLOYEE": [
      "leave:my:query",
      "leave:my:add", 
      "leave:my:edit",
      "leave:my:submit",
      "leave:my:cancel"
    ],
    "MANAGER": [
      "leave:application:query",
      "leave:application:approve",
      "leave:approval:query",
      "leave:approval:approve",
      "leave:approval:reject"
    ],
    "ADMIN": [
      "leave:*",
      "system:*"
    ],
    "ROOT": [
      "*"
    ]
  }
}
```

## 故障排除步骤

1. **检查Redis服务状态**
   ```bash
   # Windows
   netstat -ano | findstr :6379
   
   # 检查Redis进程
   tasklist | findstr redis
   ```

2. **验证Redis配置**
   - 确认Redis地址、端口、密码配置正确
   - 检查应用配置文件中的Redis连接参数

3. **检查权限缓存完整性**
   - 验证所有角色都有对应的权限缓存
   - 确认权限字符串格式正确

4. **强制刷新缓存**
   - 删除权限缓存
   - 重启应用服务
   - 重新登录用户

## 应急处理方案

### 方案1: 清除所有缓存
```redis
FLUSHDB
```
**注意**: 这会清除当前数据库的所有数据，请谨慎使用

### 方案2: 重启Redis服务
```bash
# Windows服务管理
net stop redis
net start redis
```

### 方案3: 应用层面重新加载权限
- 重启Spring Boot应用
- 触发权限数据重新加载到Redis

## 监控和预防

### 设置权限缓存监控
```redis
# 定期检查权限缓存是否存在
EXISTS "system:role_perms"

# 监控缓存大小
HLEN "system:role_perms"
```

### 建议的维护策略
1. 定期备份Redis数据
2. 监控Redis内存使用情况
3. 设置合适的缓存过期时间
4. 建立权限变更后的缓存刷新机制
