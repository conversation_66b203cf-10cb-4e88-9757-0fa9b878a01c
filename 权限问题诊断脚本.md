# 请假系统权限问题诊断指南

## 问题现象
用户访问请假系统页面时提示"访问未授权"，但确认权限已分配。

## 权限系统架构分析

### 1. 权限验证流程
```
用户请求 → Spring Security → @PreAuthorize → PermissionService.hasPerm() → Redis缓存权限检查 → 返回结果
```

### 2. 关键组件
- **PermissionService** (`@Component("ss")`): 权限验证核心服务
- **SecurityUtils**: 获取当前用户信息和角色
- **Redis缓存**: 存储角色权限映射 (`RedisConstants.System.ROLE_PERMS`)

## 诊断步骤

### 步骤1: 检查用户登录状态
```bash
# 检查用户是否正确登录，获取用户信息
GET /api/v1/auth/user/info
```

### 步骤2: 检查用户角色分配
```sql
-- 查询用户角色分配
SELECT u.username, u.nickname, r.name as role_name, r.code as role_code
FROM sys_user u
LEFT JOIN sys_user_role ur ON u.id = ur.user_id
LEFT JOIN sys_role r ON ur.role_id = r.id
WHERE u.username = '用户名';
```

### 步骤3: 检查角色权限分配
```sql
-- 查询角色权限
SELECT r.name as role_name, r.code as role_code, m.name as menu_name, m.perm
FROM sys_role r
LEFT JOIN sys_role_menu rm ON r.id = rm.role_id
LEFT JOIN sys_menu m ON rm.menu_id = m.id
WHERE r.code = '角色编码' AND m.perm LIKE 'leave:%'
ORDER BY m.perm;
```

### 步骤4: 检查Redis缓存
```bash
# 连接Redis，检查权限缓存
redis-cli
> HGETALL "system:role_perms"
> HGET "system:role_perms" "角色编码"
```

### 步骤5: 检查请假系统权限配置
需要确保以下权限已正确配置：

#### 请假申请相关权限：
- `leave:application:query` - 查询请假申请
- `leave:application:add` - 新增请假申请  
- `leave:application:edit` - 编辑请假申请
- `leave:application:delete` - 删除请假申请
- `leave:application:approve` - 审批请假申请
- `leave:application:cancel` - 取消请假申请

#### 请假类型管理权限：
- `leave:type:query` - 查询请假类型
- `leave:type:add` - 新增请假类型
- `leave:type:edit` - 编辑请假类型
- `leave:type:delete` - 删除请假类型

## 常见问题及解决方案

### 问题1: 权限已分配但仍提示未授权
**可能原因**: Redis缓存未更新
**解决方案**: 
1. 清除Redis中的权限缓存
2. 重新登录用户，触发权限缓存刷新
3. 或重启应用服务

### 问题2: 权限字符串不匹配
**可能原因**: 数据库中的权限字符串与代码中的不一致
**解决方案**: 
1. 检查`sys_menu`表中的`perm`字段值
2. 对比Controller中`@PreAuthorize`注解的权限字符串
3. 确保完全匹配（区分大小写）

### 问题3: 用户角色未正确分配
**可能原因**: `sys_user_role`表中缺少用户角色关联
**解决方案**: 
1. 在系统管理中重新分配用户角色
2. 或直接在数据库中插入用户角色关联记录

### 问题4: 超级管理员权限失效
**可能原因**: 超级管理员角色编码不正确
**解决方案**: 
1. 检查`SystemConstants.ROOT_ROLE_CODE`的值
2. 确保超级管理员用户拥有该角色编码

## 调试日志

### 启用权限调试日志
在`application-dev.yml`中添加：
```yaml
logging:
  level:
    com.rongmei.framework.security.service.PermissionService: DEBUG
    org.springframework.security: DEBUG
```

### 关键日志信息
- 权限验证失败时会输出: `用户无操作权限：权限字符串`
- 查看用户角色获取是否正常
- 查看Redis权限缓存读取是否正常

## 快速修复建议

1. **立即检查**: 登录系统管理后台，查看用户角色分配
2. **权限刷新**: 重新分配一次用户角色，触发权限缓存更新
3. **缓存清理**: 清除Redis中的权限相关缓存
4. **重新登录**: 让用户重新登录系统

## 联系开发团队
如果以上步骤都无法解决问题，请提供：
1. 用户名和角色信息
2. 具体的错误页面截图
3. 浏览器开发者工具中的网络请求错误信息
4. 服务器日志中的相关错误信息
