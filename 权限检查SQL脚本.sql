-- 请假系统权限问题诊断SQL脚本
-- 请根据实际情况修改用户名和角色编码

-- ================================
-- 1. 检查用户基本信息和角色分配
-- ================================
SELECT 
    u.id as user_id,
    u.username,
    u.nickname,
    u.status as user_status,
    r.id as role_id,
    r.name as role_name,
    r.code as role_code,
    r.status as role_status
FROM sys_user u
LEFT JOIN sys_user_role ur ON u.id = ur.user_id
LEFT JOIN sys_role r ON ur.role_id = r.id
WHERE u.username = '请替换为实际用户名'  -- 请修改为实际用户名
ORDER BY r.code;

-- ================================
-- 2. 检查请假系统菜单权限配置
-- ================================
SELECT 
    m.id,
    m.parent_id,
    m.name as menu_name,
    m.type as menu_type,
    m.perm as permission,
    m.visible,
    m.route_path,
    m.component
FROM sys_menu m
WHERE m.perm LIKE 'leave:%' OR m.route_path LIKE '/leave%'
ORDER BY m.id;

-- ================================
-- 3. 检查角色权限分配（请假相关）
-- ================================
SELECT 
    r.name as role_name,
    r.code as role_code,
    m.name as menu_name,
    m.perm as permission,
    m.type as menu_type,
    rm.create_time as assigned_time
FROM sys_role r
LEFT JOIN sys_role_menu rm ON r.id = rm.role_id
LEFT JOIN sys_menu m ON rm.menu_id = m.id
WHERE m.perm LIKE 'leave:%'
ORDER BY r.code, m.perm;

-- ================================
-- 4. 检查特定用户的请假权限
-- ================================
SELECT DISTINCT
    u.username,
    u.nickname,
    r.code as role_code,
    m.perm as permission,
    m.name as menu_name
FROM sys_user u
JOIN sys_user_role ur ON u.id = ur.user_id
JOIN sys_role r ON ur.role_id = r.id
JOIN sys_role_menu rm ON r.id = rm.role_id
JOIN sys_menu m ON rm.menu_id = m.id
WHERE u.username = '请替换为实际用户名'  -- 请修改为实际用户名
  AND m.perm LIKE 'leave:%'
ORDER BY m.perm;

-- ================================
-- 5. 检查是否缺少请假权限的角色
-- ================================
SELECT 
    r.id,
    r.name as role_name,
    r.code as role_code,
    COUNT(rm.menu_id) as leave_permission_count
FROM sys_role r
LEFT JOIN sys_role_menu rm ON r.id = rm.role_id
LEFT JOIN sys_menu m ON rm.menu_id = m.id AND m.perm LIKE 'leave:%'
WHERE r.status = 1  -- 只查询启用的角色
GROUP BY r.id, r.name, r.code
HAVING leave_permission_count = 0
ORDER BY r.code;

-- ================================
-- 6. 检查请假权限是否完整配置
-- ================================
-- 应该包含的基本权限列表
SELECT 'leave:application:query' as required_permission
UNION ALL SELECT 'leave:application:add'
UNION ALL SELECT 'leave:application:edit'
UNION ALL SELECT 'leave:application:delete'
UNION ALL SELECT 'leave:application:approve'
UNION ALL SELECT 'leave:application:cancel'
UNION ALL SELECT 'leave:type:query'
UNION ALL SELECT 'leave:type:add'
UNION ALL SELECT 'leave:type:edit'
UNION ALL SELECT 'leave:type:delete';

-- 检查哪些权限未配置
SELECT 
    required_perms.required_permission,
    CASE 
        WHEN m.perm IS NULL THEN '未配置'
        ELSE '已配置'
    END as status
FROM (
    SELECT 'leave:application:query' as required_permission
    UNION ALL SELECT 'leave:application:add'
    UNION ALL SELECT 'leave:application:edit'
    UNION ALL SELECT 'leave:application:delete'
    UNION ALL SELECT 'leave:application:approve'
    UNION ALL SELECT 'leave:application:cancel'
    UNION ALL SELECT 'leave:type:query'
    UNION ALL SELECT 'leave:type:add'
    UNION ALL SELECT 'leave:type:edit'
    UNION ALL SELECT 'leave:type:delete'
) required_perms
LEFT JOIN sys_menu m ON required_perms.required_permission = m.perm;

-- ================================
-- 7. 检查用户状态和角色状态
-- ================================
SELECT 
    u.username,
    u.nickname,
    CASE u.status 
        WHEN 1 THEN '启用'
        WHEN 0 THEN '禁用'
        ELSE '未知'
    END as user_status,
    r.code as role_code,
    CASE r.status 
        WHEN 1 THEN '启用'
        WHEN 0 THEN '禁用'
        ELSE '未知'
    END as role_status
FROM sys_user u
LEFT JOIN sys_user_role ur ON u.id = ur.user_id
LEFT JOIN sys_role r ON ur.role_id = r.id
WHERE u.username = '请替换为实际用户名';  -- 请修改为实际用户名

-- ================================
-- 8. 快速修复：为指定角色添加请假权限
-- ================================
-- 注意：执行前请确认角色编码和菜单ID正确

-- 示例：为 'EMPLOYEE' 角色添加基本请假权限
/*
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT 
    (SELECT id FROM sys_role WHERE code = 'EMPLOYEE') as role_id,
    m.id as menu_id
FROM sys_menu m
WHERE m.perm IN (
    'leave:application:query',
    'leave:application:add',
    'leave:my:query',
    'leave:my:add',
    'leave:my:edit',
    'leave:my:submit',
    'leave:my:cancel'
)
AND NOT EXISTS (
    SELECT 1 FROM sys_role_menu rm 
    WHERE rm.role_id = (SELECT id FROM sys_role WHERE code = 'EMPLOYEE')
    AND rm.menu_id = m.id
);
*/

-- ================================
-- 9. 检查超级管理员配置
-- ================================
SELECT 
    u.username,
    u.nickname,
    r.code as role_code,
    r.name as role_name
FROM sys_user u
JOIN sys_user_role ur ON u.id = ur.user_id
JOIN sys_role r ON ur.role_id = r.id
WHERE r.code = 'ROOT' OR r.code = 'ADMIN' OR r.code = 'SUPER_ADMIN';

-- ================================
-- 使用说明
-- ================================
/*
1. 将上述SQL中的 '请替换为实际用户名' 替换为遇到权限问题的实际用户名
2. 逐个执行查询，检查结果
3. 重点关注：
   - 用户是否有角色分配
   - 角色是否有请假相关权限
   - 用户和角色状态是否为启用状态
4. 如果发现权限缺失，可以使用第8部分的SQL进行修复
5. 修复后建议清除Redis缓存并重新登录
*/
